#!/usr/bin/env python3
"""
5G Security Analytics - Modal Backend
Serverless backend for real-time anomaly detection
Optimized for economic usage and high performance
"""

import modal
import os
import json
import time
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

# Modal App Configuration
app = modal.App("5g-security-analytics")

# Economical Modal Image - Only essential packages
image = (
    modal.Image.debian_slim(python_version="3.11")
    .pip_install([
        "pandas>=1.5.0",
        "numpy>=1.21.0", 
        "scikit-learn>=1.2.0",
        "joblib>=1.2.0",
        "fastapi[standard]>=0.104.0",
        "pydantic>=2.0.0"
    ])
    .run_commands([
        "mkdir -p /app/models",
        "mkdir -p /app/data"
    ])
)

# Persistent Volume for models and data (cost-effective storage)
volume = modal.Volume.from_name("5g-analytics-storage", create_if_missing=True)

# Request/Response Models
class DetectionRequest(BaseModel):
    data: Dict[str, Any]
    
class BatchRequest(BaseModel):
    batch_data: List[Dict[str, Any]]
    batch_size: Optional[int] = 100

class DetectionResponse(BaseModel):
    is_anomaly: bool
    attack_type: str
    binary_confidence: float
    processing_time_ms: float
    timestamp: float

class BatchResponse(BaseModel):
    results: List[DetectionResponse]
    total_processed: int
    total_anomalies: int
    processing_time_ms: float

class SystemStatus(BaseModel):
    models_loaded: bool
    dataset_available: bool
    processing_active: bool
    total_processed: int
    total_anomalies: int

# Global state using Modal Dict (serverless state management)
processing_state = modal.Dict.from_name("processing-state", create_if_missing=True)

@app.function(
    image=image,
    volumes={"/app/storage": volume},
    cpu=2.0,  # Economic CPU allocation
    memory=2048,  # 2GB RAM - sufficient for RandomForest
    timeout=300,  # 5 minute timeout
    container_idle_timeout=60,  # Keep warm for 1 minute (economic)
)
class AnomalyDetector:
    """
    Modal-optimized Anomaly Detector
    Loads models on container startup for efficiency
    """
    
    def __enter__(self):
        """Initialize detector on container startup"""
        import pandas as pd
        import numpy as np
        from sklearn.ensemble import RandomForestClassifier
        import joblib
        
        self.binary_model = None
        self.multiclass_model = None
        self.binary_scaler = None
        self.multiclass_scaler = None
        self.feature_columns = None
        self.attack_labels = [
            "Normal", "Flooding", "Scanning", "Injection", 
            "Impersonation", "DoS", "Other"
        ]
        
        # Load models from volume
        try:
            models_path = Path("/app/storage/models")
            if models_path.exists():
                self.binary_model = joblib.load(models_path / "binary_model.pkl")
                self.multiclass_model = joblib.load(models_path / "multiclass_model.pkl") 
                self.binary_scaler = joblib.load(models_path / "binary_scaler.pkl")
                self.multiclass_scaler = joblib.load(models_path / "multiclass_scaler.pkl")
                
                # Load feature columns
                with open(models_path / "feature_columns.json", 'r') as f:
                    self.feature_columns = json.load(f)
                    
                print("✅ Models loaded successfully from Modal volume")
            else:
                print("⚠️ Models not found in volume, will use fallback")
                self._create_fallback_models()
                
        except Exception as e:
            print(f"❌ Error loading models: {e}")
            self._create_fallback_models()
    
    def _create_fallback_models(self):
        """Create simple fallback models for demo purposes"""
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.preprocessing import StandardScaler
        import numpy as np
        
        # Create dummy models for demonstration
        self.binary_model = RandomForestClassifier(n_estimators=10, random_state=42)
        self.multiclass_model = RandomForestClassifier(n_estimators=10, random_state=42)
        self.binary_scaler = StandardScaler()
        self.multiclass_scaler = StandardScaler()
        
        # Dummy feature columns
        self.feature_columns = [
            'packet_size', 'flow_duration', 'protocol_type', 
            'src_port', 'dst_port', 'packet_count'
        ]
        
        # Train with dummy data
        dummy_data = np.random.rand(100, len(self.feature_columns))
        dummy_binary_labels = np.random.randint(0, 2, 100)
        dummy_multi_labels = np.random.randint(0, len(self.attack_labels), 100)
        
        self.binary_scaler.fit(dummy_data)
        self.multiclass_scaler.fit(dummy_data)
        self.binary_model.fit(self.binary_scaler.transform(dummy_data), dummy_binary_labels)
        self.multiclass_model.fit(self.multiclass_scaler.transform(dummy_data), dummy_multi_labels)
        
        print("✅ Fallback models created")
    
    @modal.method
    def detect_single(self, data: Dict[str, Any]) -> DetectionResponse:
        """Detect anomaly in single record"""
        start_time = time.time()
        
        try:
            # Prepare features
            features = self._prepare_features(data)
            
            # Binary prediction
            binary_proba = self.binary_model.predict_proba(features)[0]
            is_anomaly = binary_proba[1] >= 0.3  # Threshold
            
            attack_type = "Normal"
            if is_anomaly:
                # Multiclass prediction
                multi_pred = self.multiclass_model.predict(
                    self.multiclass_scaler.transform(features)
                )[0]
                attack_type = self.attack_labels[multi_pred]
            
            processing_time = (time.time() - start_time) * 1000
            
            return DetectionResponse(
                is_anomaly=is_anomaly,
                attack_type=attack_type,
                binary_confidence=float(binary_proba[1]),
                processing_time_ms=processing_time,
                timestamp=time.time()
            )
            
        except Exception as e:
            print(f"Detection error: {e}")
            return DetectionResponse(
                is_anomaly=False,
                attack_type="Error",
                binary_confidence=0.0,
                processing_time_ms=0.0,
                timestamp=time.time()
            )
    
    @modal.method
    def detect_batch(self, batch_data: List[Dict[str, Any]]) -> BatchResponse:
        """Process batch of records efficiently"""
        start_time = time.time()
        results = []
        anomaly_count = 0
        
        for record in batch_data:
            result = self.detect_single(record)
            results.append(result)
            if result.is_anomaly:
                anomaly_count += 1
        
        processing_time = (time.time() - start_time) * 1000
        
        return BatchResponse(
            results=results,
            total_processed=len(results),
            total_anomalies=anomaly_count,
            processing_time_ms=processing_time
        )
    
    def _prepare_features(self, data: Dict[str, Any]) -> List[List[float]]:
        """Prepare features for model input"""
        import numpy as np
        
        # Extract features based on available columns
        features = []
        for col in self.feature_columns:
            if col in data:
                features.append(float(data[col]))
            else:
                # Use default values for missing features
                features.append(0.0)
        
        # Scale features
        features_array = np.array([features])
        return self.binary_scaler.transform(features_array)

# Initialize detector instance
detector = AnomalyDetector()

# FastAPI Web Interface
web_app = FastAPI(
    title="5G Security Analytics API",
    description="Serverless anomaly detection for 5G networks",
    version="1.0.0"
)

@web_app.get("/")
async def root():
    """Health check endpoint"""
    return {"status": "healthy", "service": "5G Security Analytics", "platform": "Modal"}

@web_app.get("/status", response_model=SystemStatus)
async def get_status():
    """Get system status"""
    state = dict(processing_state) if processing_state else {}
    
    return SystemStatus(
        models_loaded=True,
        dataset_available=True,
        processing_active=state.get("is_processing", False),
        total_processed=state.get("total_processed", 0),
        total_anomalies=state.get("total_anomalies", 0)
    )

@web_app.post("/detect", response_model=DetectionResponse)
async def detect_anomaly(request: DetectionRequest):
    """Detect anomaly in single record"""
    try:
        result = detector.detect_single.remote(request.data)
        
        # Update global stats
        state = dict(processing_state) if processing_state else {}
        state["total_processed"] = state.get("total_processed", 0) + 1
        if result.is_anomaly:
            state["total_anomalies"] = state.get("total_anomalies", 0) + 1
        processing_state.update(state)
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Detection failed: {str(e)}")

@web_app.post("/detect/batch", response_model=BatchResponse)
async def detect_batch(request: BatchRequest):
    """Process batch of records"""
    try:
        result = detector.detect_batch.remote(request.batch_data)
        
        # Update global stats
        state = dict(processing_state) if processing_state else {}
        state["total_processed"] = state.get("total_processed", 0) + result.total_processed
        state["total_anomalies"] = state.get("total_anomalies", 0) + result.total_anomalies
        state["last_batch_time"] = time.time()
        processing_state.update(state)
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch processing failed: {str(e)}")

# Deploy FastAPI app on Modal
@app.function(
    image=image,
    cpu=1.0,  # Lighter CPU for web endpoints
    memory=512,  # 512MB for web interface
    container_idle_timeout=300,  # Keep web interface warm longer
    allow_concurrent_inputs=10  # Handle multiple requests
)
@modal.asgi_app()
def fastapi_app():
    return web_app

if __name__ == "__main__":
    # For local development
    import uvicorn
    uvicorn.run(web_app, host="0.0.0.0", port=8000)
