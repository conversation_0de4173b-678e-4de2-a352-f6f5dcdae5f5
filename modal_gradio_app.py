#!/usr/bin/env python3
"""
5G Security Analytics - Modal Gradio Control Panel
Professional control interface running on Modal
"""

import modal
import gradio as gr
import requests
import json
import time
import asyncio
from typing import Dict, <PERSON>, <PERSON><PERSON>, List
import pandas as pd
import numpy as np

# Modal App for Gradio Interface
app = modal.App("5g-gradio-control")

# Lightweight image for Gradio interface
image = (
    modal.Image.debian_slim(python_version="3.11")
    .pip_install([
        "gradio>=4.0.0",
        "requests>=2.31.0",
        "pandas>=1.5.0",
        "numpy>=1.21.0"
    ])
)

# Modal backend URL (will be set after deployment)
BACKEND_URL = "https://semskurto--5g-security-analytics-fastapi-app.modal.run"

class ModalControlPanel:
    """Professional Gradio control panel for Modal backend"""
    
    def __init__(self):
        self.processing_active = False
        self.total_processed = 0
        self.total_anomalies = 0
        self.current_batch = 0
        
    def get_backend_status(self) -> Dict[str, Any]:
        """Get status from Modal backend"""
        try:
            response = requests.get(f"{BACKEND_URL}/status", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"Backend returned {response.status_code}"}
        except Exception as e:
            return {"error": f"Cannot connect to backend: {str(e)}"}
    
    def generate_sample_data(self, count: int = 100) -> List[Dict[str, Any]]:
        """Generate sample 5G network data for demonstration"""
        np.random.seed(42)  # For reproducible results
        
        sample_data = []
        for i in range(count):
            # Simulate realistic 5G network metrics
            record = {
                "packet_size": np.random.randint(64, 1500),
                "flow_duration": np.random.exponential(1000),
                "protocol_type": np.random.choice([6, 17, 1]),  # TCP, UDP, ICMP
                "src_port": np.random.randint(1024, 65535),
                "dst_port": np.random.choice([80, 443, 53, 22, 21, 25]),
                "packet_count": np.random.randint(1, 100),
                "bytes_sent": np.random.randint(100, 10000),
                "bytes_received": np.random.randint(100, 10000),
                "connection_state": np.random.choice([0, 1, 2, 3]),
                "service_type": np.random.choice([0, 1, 2, 3, 4])
            }
            
            # Add some anomalous patterns (10% of data)
            if np.random.random() < 0.1:
                record["packet_size"] = np.random.randint(1400, 1500)  # Large packets
                record["packet_count"] = np.random.randint(100, 1000)  # High count
                record["flow_duration"] = np.random.exponential(5000)  # Long duration
            
            sample_data.append(record)
        
        return sample_data
    
    def test_single_detection(self) -> Tuple[str, str, str]:
        """Test single record detection"""
        try:
            # Generate single test record
            test_data = self.generate_sample_data(1)[0]
            
            # Send to backend
            response = requests.post(
                f"{BACKEND_URL}/detect",
                json={"data": test_data},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                status = "✅ Detection successful"
                details = f"""
Test Result:
• Anomaly: {'Yes' if result['is_anomaly'] else 'No'}
• Attack Type: {result['attack_type']}
• Confidence: {result['binary_confidence']:.3f}
• Processing Time: {result['processing_time_ms']:.1f}ms
                """.strip()
                return status, details, "Single detection completed"
            else:
                return "❌ Detection failed", f"Error: {response.status_code}", "Test failed"
                
        except Exception as e:
            return "❌ Connection error", f"Error: {str(e)}", "Test failed"
    
    def start_batch_processing(self, batch_size: int, num_batches: int, 
                             delay_seconds: float) -> Tuple[str, str, str]:
        """Start batch processing simulation"""
        try:
            self.processing_active = True
            self.current_batch = 0
            
            total_processed = 0
            total_anomalies = 0
            
            for batch_num in range(num_batches):
                if not self.processing_active:
                    break
                
                self.current_batch = batch_num + 1
                
                # Generate batch data
                batch_data = self.generate_sample_data(batch_size)
                
                # Send batch to backend
                response = requests.post(
                    f"{BACKEND_URL}/detect/batch",
                    json={"batch_data": batch_data, "batch_size": batch_size},
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    total_processed += result['total_processed']
                    total_anomalies += result['total_anomalies']
                    
                    print(f"Batch {batch_num + 1}/{num_batches} completed: "
                          f"{result['total_processed']} records, "
                          f"{result['total_anomalies']} anomalies")
                else:
                    print(f"Batch {batch_num + 1} failed: {response.status_code}")
                
                # Delay between batches
                time.sleep(delay_seconds)
            
            self.processing_active = False
            self.total_processed = total_processed
            self.total_anomalies = total_anomalies
            
            status = "✅ Batch processing completed"
            details = f"""
Processing Summary:
• Total Batches: {num_batches}
• Total Records: {total_processed:,}
• Total Anomalies: {total_anomalies:,}
• Anomaly Rate: {(total_anomalies/max(total_processed,1)*100):.1f}%
            """.strip()
            
            return status, details, "Batch processing completed"
            
        except Exception as e:
            self.processing_active = False
            return "❌ Processing error", f"Error: {str(e)}", "Processing failed"
    
    def stop_processing(self) -> Tuple[str, str, str]:
        """Stop current processing"""
        self.processing_active = False
        return "⏹️ Processing stopped", "Processing stopped by user", "Stopped"
    
    def get_current_stats(self) -> str:
        """Get current processing statistics"""
        backend_status = self.get_backend_status()
        
        if "error" in backend_status:
            return f"❌ Backend Error: {backend_status['error']}"
        
        return f"""
🛡️ 5G Security Analytics - Live Status

Backend Status:
• Models: {'✅ Loaded' if backend_status.get('models_loaded') else '❌ Not Loaded'}
• Dataset: {'✅ Available' if backend_status.get('dataset_available') else '❌ Not Available'}
• Processing: {'🔄 Active' if self.processing_active else '⏸️ Ready'}

Current Session:
• Current Batch: {self.current_batch}
• Session Processed: {self.total_processed:,}
• Session Anomalies: {self.total_anomalies:,}

Global Statistics:
• Total Processed: {backend_status.get('total_processed', 0):,}
• Total Anomalies: {backend_status.get('total_anomalies', 0):,}
• Global Anomaly Rate: {(backend_status.get('total_anomalies', 0)/max(backend_status.get('total_processed', 1), 1)*100):.1f}%

Platform: Modal Serverless ⚡
        """.strip()

def create_gradio_interface():
    """Create professional Gradio interface"""
    
    control_panel = ModalControlPanel()
    
    # Professional theme and styling
    theme = gr.themes.Soft(
        primary_hue="blue",
        secondary_hue="gray",
        neutral_hue="slate"
    )
    
    custom_css = """
    .gradio-container {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 1200px !important;
    }
    .status-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin: 10px 0;
    }
    """
    
    with gr.Blocks(
        title="5G Security Analytics - Professional Control Panel",
        theme=theme,
        css=custom_css
    ) as demo:
        
        # Header with simulation indicator
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin-bottom: 20px;">
            <h1>🛡️ 5G Security Analytics</h1>
            <h3>Professional Control Panel - SIMULATION MODE</h3>
            <p>Powered by Modal Serverless Platform | Hugging Face Agents-MCP-Hackathon Track 3</p>
        </div>
        """)
        
        # System Status Section
        with gr.Row():
            with gr.Column(scale=2):
                status_display = gr.Textbox(
                    label="📊 System Status",
                    lines=12,
                    interactive=False,
                    show_copy_button=True
                )
            with gr.Column(scale=1):
                gr.Markdown("### 🎛️ Quick Actions")
                refresh_btn = gr.Button("🔄 Refresh Status", variant="secondary", size="lg")
                test_btn = gr.Button("🧪 Test Detection", variant="primary", size="lg")
        
        # Configuration Section
        gr.Markdown("## ⚙️ Processing Configuration")
        
        with gr.Row():
            batch_size = gr.Slider(
                minimum=10, maximum=500, value=100, step=10,
                label="Batch Size",
                info="Records per batch (10-500)"
            )
            num_batches = gr.Slider(
                minimum=1, maximum=50, value=10, step=1,
                label="Number of Batches", 
                info="Total batches to process (1-50)"
            )
            delay_seconds = gr.Slider(
                minimum=0.1, maximum=5.0, value=1.0, step=0.1,
                label="Batch Delay (seconds)",
                info="Delay between batches (0.1-5.0s)"
            )
        
        # Control Buttons
        gr.Markdown("## 🎮 Processing Controls")
        
        with gr.Row():
            start_btn = gr.Button("🚀 Start Batch Processing", variant="primary", size="lg")
            stop_btn = gr.Button("⏹️ Stop Processing", variant="stop", size="lg")
        
        # Results Section
        with gr.Row():
            operation_status = gr.Textbox(label="Operation Status", interactive=False)
            operation_details = gr.Textbox(label="Details", lines=6, interactive=False)
        
        # Information Panel
        with gr.Accordion("📖 User Guide & Information", open=False):
            gr.Markdown("""
            ### 🚀 Quick Start Guide
            1. **Check Status**: Click "Refresh Status" to verify backend connection
            2. **Test Detection**: Use "Test Detection" to verify single record processing
            3. **Configure Processing**: Set batch size, number of batches, and delay
            4. **Start Processing**: Click "Start Batch Processing" to begin simulation
            5. **Monitor Progress**: Watch real-time statistics and processing status
            
            ### 🎯 Hackathon Features
            - **Serverless Architecture**: Powered by Modal platform for automatic scaling
            - **Economic Usage**: Pay-per-use pricing with optimized resource allocation
            - **Real-time Processing**: Live anomaly detection with instant feedback
            - **Professional Interface**: Clean, intuitive control panel design
            - **Simulation Mode**: Generate realistic 5G network data for demonstration
            
            ### 🔧 Technical Details
            - **Backend**: Modal serverless functions with FastAPI
            - **Models**: RandomForest-based anomaly detection
            - **Scaling**: Automatic horizontal scaling based on demand
            - **Storage**: Persistent Modal volumes for model storage
            
            ### 📊 Dashboard Access
            For real-time monitoring, access the Streamlit dashboard at:
            `https://your-modal-streamlit-url.modal.run`
            """)
        
        # Event Handlers
        def refresh_status():
            return control_panel.get_current_stats()
        
        def test_detection():
            return control_panel.test_single_detection()
        
        def start_processing(batch_size_val, num_batches_val, delay_val):
            return control_panel.start_batch_processing(
                int(batch_size_val), int(num_batches_val), float(delay_val)
            )
        
        def stop_processing():
            return control_panel.stop_processing()
        
        # Wire up events
        refresh_btn.click(
            fn=refresh_status,
            outputs=[status_display]
        )
        
        test_btn.click(
            fn=test_detection,
            outputs=[operation_status, operation_details, operation_status]
        )
        
        start_btn.click(
            fn=start_processing,
            inputs=[batch_size, num_batches, delay_seconds],
            outputs=[operation_status, operation_details, operation_status]
        )
        
        stop_btn.click(
            fn=stop_processing,
            outputs=[operation_status, operation_details, operation_status]
        )
        
        # Auto-refresh on load
        demo.load(
            fn=refresh_status,
            outputs=[status_display]
        )
        
        # Auto-refresh timer (every 3 seconds)
        timer = gr.Timer(3.0)
        timer.tick(
            fn=refresh_status,
            outputs=[status_display]
        )
    
    return demo

# Deploy Gradio app on Modal
@app.function(
    image=image,
    cpu=1.0,
    memory=1024,
    container_idle_timeout=600,  # Keep warm for 10 minutes
    allow_concurrent_inputs=5
)
@modal.web_server(8000)
def gradio_app():
    """Deploy Gradio interface on Modal"""
    demo = create_gradio_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=8000,
        share=False,
        show_error=True
    )

if __name__ == "__main__":
    # For local development
    demo = create_gradio_interface()
    demo.launch(server_name="127.0.0.1", server_port=7860)
